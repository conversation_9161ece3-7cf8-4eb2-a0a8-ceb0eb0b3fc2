后端插件开发规范



# 插件代码开发规范

本文档旨在为项目团队提供统一的插件开发规范，以确保插件开发的完整性。

## 1 插件的基础信息

### 1.2 插件的产品线

- 插件归属于哪个产品线

### 1.3 插件的唯一标识

- 插件的唯一标识，可以简单理解成插件的名称-不可重复
- 每一个插件件都需要有唯一标识，该标识整个软件研发中心统一管理（后续由插件管理市场维护），例如：PQ通用组件可以申请标识：pqbasicplugin(这是示例)

### 1.4 插件的版本

- 插件对应的版本

### 1.5 插件的描述文件

- plugin.properties,内容由以上的属性组成

```
plugin.productname=productName 产品线-必填
plugin.version=1.0.0 版本-必填
plugin.name=ibseventlog  插件标识-必填
plugin.runningtype=jar 默认
```

- 描述文件存放的位置，需要在插件的resources目录下，详情请看插件的结构。
- 配置文件存放路径：统一放置于core包下的resources/config目录下即可

```
--src
  --main
    --resources
      --config
        --plugin.properties
```

## 2.插件开发

- 插件具备什么样的功能，以及满足什么样的业务场景，耦合度上需要业务团队自行斟酌。
- 插件是标准化的功能组件，原则上尽量满足独立的前提。

### 2.1 概述

- 插件本质上只需要满足spring的自动装配即可。但是由于平台是具备管理的属性。因此会对部分内容进行控制。
  因此开发的过程中，部分内容需要遵守平台的规范，否则会出现无法被加载的问题。
  开发同学可以按照以下顺序进行开发

### 2.2 建立maven工程

- maven工程主要是分为几个模块。parent,web,common,core。
- parent主要是起管理的作用，管理模块以及版本控制。
- common，存放实体类，枚举等。
- service，底层的逻辑实现，侧重点主要是避免插件耦合。如果插件需要复用其他插件的能力，引入service包即可。
- core，插件的完整实现
- web,一个纯粹的spring-web工程，主要是用于加载插件，进行代码调试。
  完整的结构如下.开发人员可以自行调整，但是core与web是必须存在的。

```
--parent
  --common
  --core
  --web
  --service
```

- mvn工程的相关信息
- groupId=com.cet.electric 这个是必须匹配的。
- artifactId不做要求
- version a.b.c.d
- 接下来，进入开发的流程。以下内容都是core的相关内容。

### 2.3 融合平台依赖控制

- 需要引入融合平台的依赖控制。后续在开发时，需要引入对应的jar包。这里仅做依赖版本控制。后续引入哪些依赖，再具体展开。

```
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>fusion-matrix-v2-parent</artifactId>
                <version>1.0.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        <dependencies>
    <dependencyManagement>
```

### 2.4 spring自动装配

- 自动装配的实现方式很多。因此只提供一个demo以供参考，以spring.factories的方式
- 配置类

```
package a.b.c;

@Configuration
@ComponentScan(basePackages = {"需要被加载的包"}， nameGenerator = PowerCloudEventBeanNameGenerator.class)
public class CoreApplication {

}
```

- spring.factories spring框架加载配置类的方式之一

```
需要在src/main/resources目录下构建对应的目录以及文件
完整路径如下:
--src
  --main
    --resources
      --META-INF
        --spring.factories


spring.factories文件的具体内容:
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
a.b.c.CoreApplication
```

### 2.5 beanName

- 由于融合平台只是一个加载插件的平台，如果bean的名字重复，会造成故障。因此需要插件开发的时候，进行规避，避免beanName重复
- 如何解决beanName重复？
  答：统一使用AnnotationBeanNameGenerator。为bean的名称添加插件标识前缀。
  如插件唯一标识为“ powercloud-event”。则前缀为powercloud-event_
  代码demo如下

```
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;

public class PowercloudEventBeanNameGenerator implements BeanNameGenerator {
    private static String pluginNamePrefix = "powercloud-event_";
    @Override
    public @NotNull String generateBeanName(@NotNull BeanDefinition beanDefinition, @NotNull BeanDefinitionRegistry beanDefinitionRegistry) {
        AnnotationBeanNameGenerator annotationBeanNameGenerator =  new AnnotationBeanNameGenerator();
        return pluginNamePrefix + annotationBeanNameGenerator.generateBeanName(beanDefinition, beanDefinitionRegistry);
    }
}
```

- 通过容器以beanName获取bean。
  以下demo只是想说明beanName的格式。

```
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

public class Main {
    public static void main(String[] args) {
        ApplicationContext context = new AnnotationConfigApplicationContext(AppConfig.class);
        
        // 获取 Bean，注意 Bean 名称带有前缀
        MyBean myBean = (MyBean) context.getBean("powercloud-event_myBean");
        
        // 使用 Bean
        myBean.doSomething();
    }
}
```

- resource注解的使用,beanName需要调整

```
    @Resource(name = "powercloud-event_someBean")
    private SomeBean someBean;
```

### 2.6 controller

- 开发一个controller
  组件内所有url都要有统一的前缀，该前缀与插件唯一标识挂钩：/ibsrecharge/**,这样子可以与其他插件避免url重复的问题
  例如：/ibsrecharge/api/overview

```
@RestController
@RequestMapping("/ibsrecharge/api/")
@Api(value = "project API", tags = "充值缴费信息")
public class IbsRechargeController {

    @Autowired
    private WalletApiService walletApiService;

    @ApiOperation(value = "钱包账户总览", notes = "钱包账户总览")
    @PostMapping("/overview")
    @RequestLogger(requestCode = RequestCodeConstants.GET_WALLET_ACCOUNT_OVERVIEW)
    public ResponseDTO<WalletAccountOveDTO> queryWalletAccountOverView(@RequestHeader(Constants.USER_ID) Integer userId,
                                                                       @RequestBody WalletAccountOveRequestDTO walletAccountOveRequestDTO) {
        return walletApiService.queryWalletAccountOverView(walletAccountOveRequestDTO);
    }
}
```

### 2.7 路由-注册信息上报

- 融合平台对于controller的控制，侧重点是在于控制路由。
- 融合平台采取的机制是上报，因此开发人员需要将controller的路径资源进行上报即可。
- 如何上报？-实现配置类即可。但是首先需要引入依赖

```
            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>fusion-matrix-v2-client</artifactId>
                <version>1.0.10</version>
            </dependency>
```

- 实现上报相关的配置类-详解请看demo代码中的注解

```
package com.cet.electric.ibscloud.ibsrecharge.core.config;


import com.cet.electric.fusion.matrix.v2.client.register.JarPluginRegister;
import com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PluginConfiguration extends JarPluginRegister {
    @Override
    public PluginRuntimeInfo getPluginRuntimeInfo() {
        PluginRuntimeInfo pluginRuntimeInfo = new PluginRuntimeInfo();
        //设置插件的url前缀,用于路由,url前缀需要与插件唯一标识保持一致
        pluginRuntimeInfo.setPluginUrlPrefex("/ibsrecharge/**");
        //插件唯一标识
        pluginRuntimeInfo.setPluginname("ibsrecharge");
        //插件的产品线
        pluginRuntimeInfo.setProductname("ibs-cloud");
        return pluginRuntimeInfo;
    }
}
```

- 为什么需要上报？
  因为插件可以随意安装在某个服务中。服务并不是固定的。这里为了后续拓展而考虑。
  所以前端的路由路径也是以 插件唯一标识作为前缀，而不是以服务名称作为前缀。
  如，http://10.12.137.7:8300/fusion-gateway/powercloud/event/api/node/nodeTree/device/all。 
  以上url中powercloud为插件标识，而不是服务名称。
  如果以传统的方式设计，采取自动上报的方式(网关会去注册中心拉取服务列表)，url如下，http://10.12.137.7:8300/fusion-gateway/平台服务名称/powercloud/event/api/node/nodeTree/device/all。 
  那么如果后面插件部署在不同服务中，前端是需要不断调整，因此需要采取以手动上报的方式，这样子网关就可以根据插件唯一标识---服务的关系，进行自定义的路由。
- 上报的时机
  只有平台启动后，才会进行上报。

### 2.8 定时任务

- 定时任务启动机制
  只有容器完全启动后，定时任务才会开始运行。
- 线程池-线程数控制
  统一由平台的application.yml控制。
  fusion.schedule.core.processor.num默认为8。
  业务团队需要自行斟酌线程池数量，过大也没什么意义。只会导致频繁上下文切换，同时也占用更多的内存。

### 2.9 配置

- 配置分为俩类。一部分是公共的，如框架自身的配置，另外一部分则是业务配置。
- 公共配置，统一由平台的application.yml进行管控

```
如果存在平台的公共配置存在缺漏，需要与平台的人员沟通，进行补充。
```

- 业务配置，需要以插件标识作为最高级的前缀,避免配置出现重复复盖的问题。如插件唯一标识为ibs-pay
  那么其配置如下

```
ibs-pay:
  xx:
    xx:
      xx:
```

### 2.10 配置文件(application.yml)

- 配置文件命名：配置文件统一以 application-插件唯一标识.yml 的格式
- 配置文件存放路径：统一放置于core包下的resource/config目录下即可

```
--src
  --main
    --resources
      --config
        -- application-xxx.yml
```

### 2.11 异常处理

- 平台的异常统一由平台来进行管控，开发者无需关注异常捕获器。
- 引入依赖

```
			<dependency>
				<groupId>com.cet.electric</groupId>
				<artifactId>fusion-matrix-v2-common</artifactId>
			</dependency>
```

- 异常实体类

```
com.cet.electric.fusion.matrix.v2.dto.exception.ErrorMsg
```

- 注意事项
  抛异常时,请使用平台提供的异常实体类，否则均标记为未知异常。
  平台对于异常的处理均为输出日志，若有特殊处理的情况，请自行捕获&处理。

### 2.12 swagger

- 引入依赖,目前使用的版本为2.9.2

```
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
```

- 配置类
  通用组件swagger需要有特殊的group命名，否则group冲突之后会导致程序启动失败
  代码如下

```
@Configuration
@EnableSwagger2
public class PowerManageSwaggerConfig {

    @Bean
    public Docket createPowerCloudEventManageDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("事件插件")
                .apiInfo(apiInfo())
                .enable(true)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cet.electric.powercloud.event.core.api"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("事件插件接口定义说明")
                .description("主要是提供事件相关接口。")
                .version("1.0")
                .build();
    }
}
```

### 2.13 公共资源

- 公共资源的bean注入需要谨慎

```
例如，threadpool,redistemplate,datasource等不得定义为primary
例如，threadpool,redistemplate,datasource等需标注ConditionOnMissingBean,避免bean冲突导致程序启动失败（除非是有特殊用途，需特殊处理）
```

### 2.14 日志文件

- 由于融合平台后续会加载许多插件，为了方便大家查看日志。因此支持将日志文件以插件的形式进行切割。
- 首先引入依赖

```
            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>fusion-matrix-v2-client</artifactId>
            </dependency>
```

- 实现如下接口即可。同时注入容器即可。

```
com.cet.electric.fusion.matrix.v2.client.logger.AbstractLoggerRegister
```

- 注意事项
  console 是否打印到控制台。建议不要打到控制台-生产环境
  packageName-以插件的顶级包名为准。如com.cet.electric.ibs.pay
  LoggerTypeDefine 则是标识，可以自行选择
  fileName 日志文件的名字
  另外日志的打印形式是以异步的形式，查看日志时可以稍微等待。

```
public class CustomerLoggerDefine {
    private String。packageName;

    private String fileName;

    private。LoggerTypeDefine loggerTypeDefine;
    /*
    * 是否打印到控制台
    * */
    private boolean。console;
}
```

- 代码demo

```
@Component
public class BaseConfigLogRegister extends AbstractLoggerRegister {

    @Override
    public CustomerLoggerDefine getCustomerLoggerDefine() {
        CustomerLoggerDefine customerLoggerDefine = new CustomerLoggerDefine();
        customerLoggerDefine.setLoggerTypeDefine(LoggerTypeDefine.api);
        customerLoggerDefine.setFileName("base-config");
        customerLoggerDefine.setPackageName("com.cet.electric.baseconfig");
        customerLoggerDefine.setConsole(Boolean.TRUE);
        return customerLoggerDefine;
    }
}
```

- 日志文件位置
  在容器中的/var/log/matterhorn/路径下。如何挂载开发人员自行设置。

## 2.15 security

- springsecurity的相关内容，不需要考虑。融合平台本身已做限制。

## 2.16 避免耦合

- 插件不可避免会存在功能重复的问题。
  这里提供的建议如下：
  插件在开发时，service层相关的内容进行抽取。service包用于后续提供给其他插件使用。
  core包其实提供的web层的能力。如果需要底层进行复用，使用service即可。

## 2.17 总线

- 总线的本质其实就是轻耦合的前提下，让插件可以通信。
  [总线文档位置](https://cetsoft-svr1/Platforms/PLT-Matterhorn/_wiki/wikis/PLT-Matterhorn.wiki/1394/业务融合-总线组件)
- 注意事项
  生产者与消费者需要在一个容器中。

## 2.18 插件相互引用

```
不要直接引用核心包。引用接口包即可。
开发人员需要部署时，能知道对应的插件是否存在。
```

## 3.调试

## 3.1 本地调试

- 利用工程中的web服务，引入插件即可，就不过多赘述了。